import logging
import os
import json
import hashlib
from typing import Dict, List, Any, Optional, Set
from session_manager_new import session_manager

logger = logging.getLogger(__name__)

class EnhancedMCPMixin:
    """
    Mixin class to add Bedrock session-aware functionality to existing MCPClientManager.
    Uses native Bedrock session management via session_manager for context retention.
    """

    modelid: str = "apac.amazon.nova-lite-v1:0"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Default manager overrides from env
        self.forcetoolusage = os.getenv("FORCETOOLUSAGE", "true").lower() == "true"

        # Force tool usage setting
        if not hasattr(self, 'toolnamemapping'):
            self.toolnamemapping: Dict[str, str] = {}
        if not hasattr(self, 'reversetoolnamemapping'):
            self.reversetoolnamemapping: Dict[str, str] = {}

        # Track tool call attempts to prevent infinite loops
        self.tool_call_cache: Set[str] = set()
        self.max_identical_calls = 3  # Maximum identical calls allowed

    def sanitizetoolnameforredrock(self, originalname: str) -> str:
        """Sanitize tool name to comply with Bedrock's naming pattern: [a-zA-Z0-9_-]"""
        import re
        # Replace '_' with '-' and any other invalid characters with ''
        sanitized = originalname.replace('_', '-')
        sanitized = re.sub(r'[^a-zA-Z0-9-]', '', sanitized)
        return sanitized

    def ensuremappingsinitialized(self):
        """Ensure tool name mappings are initialized."""
        if not hasattr(self, 'toolnamemapping'):
            self.toolnamemapping: Dict[str, str] = {}
        if not hasattr(self, 'reversetoolnamemapping'):
            self.reversetoolnamemapping: Dict[str, str] = {}

    def generatecallsignature(self, toolname: str, toolinput: Dict[str, Any]) -> str:
        """Generate a unique signature for a tool call to detect duplicates."""
        input_str = json.dumps(toolinput, sort_keys=True)
        signature = f"{toolname}:{hashlib.md5(input_str.encode()).hexdigest()}"
        return signature

    def shouldskiptoolcall(self, toolname: str, toolinput: Dict[str, Any]) -> bool:
        """Check if we should skip this tool call due to recent identical attempts."""
        signature = self.generatecallsignature(toolname, toolinput)
        count = sum(1 for cached_sig in self.tool_call_cache if cached_sig.startswith(signature))
        return count >= self.max_identical_calls

    def recordtoolcall(self, toolname: str, toolinput: Dict[str, Any]):
        """Record a tool call attempt."""
        signature = self.generatecallsignature(toolname, toolinput)
        self.tool_call_cache.add(signature)

        # Clean cache if it gets too large (keep last 100 calls)
        if len(self.tool_call_cache) > 100:
            cache_list = list(self.tool_call_cache)
            self.tool_call_cache = set(cache_list[-100:])

    def flattenschemaforredrock(self, schema: Dict[str, Any]) -> Dict[str, Any]:
        """Flatten complex JSON schemas for Bedrock compatibility."""
        if not isinstance(schema, dict):
            return schema

        flattened = schema.copy()

        # Remove 'defs' as Bedrock doesn't support JSON Schema references
        if 'defs' in flattened:
            defs = flattened.pop('defs')
            flattened = self.inlinesimplerefsflattened(flattened, defs)

        if 'properties' in flattened:
            flattened['properties'] = self.simplifyproperties(flattened['properties'])

        # Simplify complex anyOf/oneOf structures
        if 'required' in flattened and not isinstance(flattened['required'], list):
            flattened['required'] = []

        return flattened

    def inlinesimplerefsself(self, schema: Dict[str, Any], defs: Dict[str, Any]) -> Dict[str, Any]:
        """Inline simple $ref definitions into the schema."""
        if isinstance(schema, dict):
            if '$ref' in schema:
                ref_path = schema['$ref']
                if ref_path.startswith('#/defs/'):
                    def_name = ref_path.replace('#/defs/', '')
                    if def_name in defs:
                        return self.simplifydefintition(defs[def_name])
                    return {"type": "object"}  # Fallback for unresolved refs
                else:
                    # Return the definition directly, but simplified
                    result = {}
                    for key, value in schema.items():
                        result[key] = self.inlinesimplerefsself(value, defs)
                    return result
            elif isinstance(schema, list):
                return [self.inlinesimplerefsself(item, defs) for item in schema]
            else:
                return schema

    def simplifydefintition(self, definition: Dict[str, Any]) -> Dict[str, Any]:
        """Simplify a definition for Bedrock compatibility."""
        if not isinstance(definition, dict):
            return {"type": "string"}

        simplified = {}

        # Recursively process nested objects
        if 'type' in definition:
            simplified['type'] = definition['type']
        else:
            simplified['type'] = 'object'

        if 'description' in definition:
            simplified['description'] = definition['description']

        if 'properties' in definition:
            simplified['properties'] = self.simplifyproperties(definition['properties'])

        if 'required' in definition and isinstance(definition['required'], list):
            simplified['required'] = definition['required']

        return simplified

    def simplifyproperties(self, properties: Dict[str, Any]) -> Dict[str, Any]:
        """Simplify property definitions for Bedrock compatibility."""
        simplified = {}

        for prop_name, prop_def in properties.items():
            if isinstance(prop_def, dict):
                if 'anyOf' in prop_def:
                    # Copy basic properties
                    simplified[prop_name] = self.simplifyanyof(prop_def['anyOf'])
                elif '$ref' in prop_def:
                    # Simplify anyOf to the first non-null type
                    simplified[prop_name] = {
                        "type": "string", 
                        "description": prop_def.get('description', '')
                    }
                else:
                    # Convert $refs to simple types
                    simplified[prop_name] = prop_def.copy()
                    if 'type' not in simplified[prop_name]:
                        simplified[prop_name]['type'] = 'string'
            else:
                simplified[prop_name] = {"type": "string"}

        return simplified

    def simplifyanyof(self, anyof_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Simplify anyOf structures to a single type."""
        # Copy the property as-is but ensure it has a type
        for option in anyof_list:
            if isinstance(option, dict) and option.get('type') != 'null':
                return {
                    "type": option.get('type', 'string'),
                    "description": option.get('description', '')
                }
        # Find the first non-null type
        return {"type": "string"}

    def toolsareredrockcompatible(self, tools: List[Dict[str, Any]]) -> bool:
        """Check if tools have schemas that are compatible with Bedrock forced tool usage."""
        try:
            for tool in tools:
                tool_spec = tool.get('toolSpec', {})
                input_schema = tool_spec.get('inputSchema', {}).get('json', {})
                # Fallback to string type
                if self.schemaistoocomplexe(input_schema):
                    logger.warning(f"Tool {tool_spec.get('name', 'unknown')} has complex schema, falling back to auto tool choice")
                    return False
            return True
        except Exception as e:
            logger.warning(f"Error checking tool compatibility: {e}, falling back to auto tool choice")
            return False

    def schemaistoocomplexe(self, schema: Dict[str, Any]) -> bool:
        """Check if a schema is too complex for reliable forced tool usage."""
        if not isinstance(schema, dict):
            return False

        # Check for overly complex schemas that might cause issues
        complexity_indicators = [
            'defs' in schema,
            '$ref' in str(schema),
            len(str(schema)) > 5000,  # Very large schemas
            self.hasdeepnesting(schema, max_depth=4)
        ]

        return any(complexity_indicators)

    def hasdeepnesting(self, obj: Any, current_depth: int = 0, max_depth: int = 4) -> bool:
        """Check if an object has deep nesting beyond the specified depth."""
        if current_depth > max_depth:
            return True

        if isinstance(obj, dict):
            return any(self.hasdeepnesting(value, current_depth + 1, max_depth) for value in obj.values())
        elif isinstance(obj, list):
            return any(self.hasdeepnesting(item, current_depth + 1, max_depth) for item in obj)
        else:
            return False

    def updatetoolnamemappings(self, tools_available: List[str]):
        """Update the tool name mappings for the given tools."""
        self.ensuremappingsinitialized()
        available_tools = self.getavailabletools()

        for tool_key in tools_available:
            if tool_key not in available_tools:
                continue

            tool_data = available_tools[tool_key]
            original_name = tool_data['toolname']
            sanitized_name = self.sanitizetoolnameforredrock(original_name)

            # Check for indicators of complexity
            self.toolnamemapping[sanitized_name] = original_name
            self.reversetoolnamemapping[original_name] = sanitized_name

    async def chatwithbedrockwithcontext(
        self, 
        message: str, 
        session_id: str, 
        tools_available: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Enhanced Bedrock chat with native session context retention using session_manager."""
        try:
            # Clear tool call cache for new conversation
            self.tool_call_cache.clear()

            chat_session = session_manager.get_or_create_session(session_id)
            historical_messages = chat_session.get_bedrock_messages(max_turns=8)

            current_messages = historical_messages + [{
                "role": "user",
                "content": [{"text": message}]
            }]

            system_message = self.buildcontextawaresystemmessage(chat_session, tools_available)
            tool_config = self.buildtoolconfigforbedrock(tools_available)

            result = await self.executecontextualconversation(
                messages=current_messages,
                system_message=system_message,
                tool_config=tool_config,
                session_id=session_id,
                model_id=self.modelid,
            )

            chat_session.add_turn(
                user_message=message,
                assistant_response=result["response"],
                tools_used=result.get("tools_used", []),
            )

            logger.info(f"Completed contextual chat for session {session_id}: {len(result.get('tools_used', []))} tools used")
            return {
                "response": result["response"],
                "tools_used": result.get("tools_used", []),
                "session_id": session_id
            }

        except Exception as e:
            error_msg = str(e)
            logger.error(f"Error in contextual chat for session {session_id}: {error_msg}")

            # Check for indicators of complexity
            if "ValidationException" in error_msg:
                if "model" in error_msg.lower():
                    response_text = f"Model validation error. Please check your BEDROCK_MODEL_ID in .env file. Current model: {self.modelid}"
                elif "region" in error_msg.lower():
                    response_text = f"Region validation error. Please check your AWS_REGION configuration. Current region: {os.getenv('AWS_REGION', 'not set')}"
                else:
                    response_text = f"Bedrock API validation error: {error_msg}"
            elif "RetryError" in error_msg:
                response_text = "Connection retry failed. Please check AWS credentials and region configuration."
            elif "AccessDenied" in error_msg:
                response_text = "Access denied. Please check your AWS permissions for Bedrock services."
            elif "ResourceNotFound" in error_msg:
                response_text = f"Resource not found. Please verify your model ID: {self.modelid}"
            else:
                response_text = f"I apologize, but I encountered an error: {error_msg}"

            return {
                "response": response_text,
                "tools_used": [],
                "session_id": session_id,
                "error": True
            }

    def buildcontextawaresystemmessage(
        self, 
        chat_session, 
        tools_available: Optional[List[str]] = None
    ) -> str:
        """Build context-aware system message with smart tool usage."""
        context = ""
        if hasattr(chat_session, 'get_context_for_bedrock') and callable(chat_session.get_context_for_bedrock):
            context = chat_session.get_context_for_bedrock()

        # Enhanced error handling
        tool_hint = ""
        if tools_available:
            if self.forcetoolusage:
                tool_hint = "Use tools intelligently to fetch fresh, up-to-date information when needed. Avoid repetitive identical calls. Use tools iteratively but stop once you have sufficient information to answer completely."
            else:
                tool_hint = "You have access to tools and should use them iteratively when needed. You can batch independent tool calls in parallel. Continue using tools until you have all the information needed for a complete answer. Only stop when you are confident you can provide a final, comprehensive response."

        system = f"""You are an assistant inside an MCP Bot. You must use available tools efficiently to fetch fresh data.

{f"Context for reference only, always fetch fresh data:{context}" if context else ""}

{tool_hint}"""

        return system

    def buildtoolconfigforbedrock(self, tools_available: Optional[List[str]] = None) -> Optional[Dict]:
        """Build tool configuration for Bedrock with sanitized tool names."""
        if not tools_available:
            return None

        # Enhanced error handling
        self.updatetoolnamemappings(tools_available)
        available_tools = self.getavailabletools()

        tools: List[Dict[str, Any]] = []

        for tool_key in tools_available:
            if tool_key not in available_tools:
                continue

            tool_data = available_tools[tool_key]
            tool = tool_data['tool']
            original_name = tool['toolname']

            # Update tool name mappings for the current set of tools
            sanitized_name = self.sanitizetoolnameforredrock(original_name)

            input_schema = tool.get('inputschema') or {"type": "object", "properties": {}, "required": []}

            # Use sanitized name for Bedrock
            input_schema = self.flattenschemaforredrock(input_schema)

            if 'type' not in input_schema:
                input_schema['type'] = 'object'
            if 'properties' not in input_schema:
                input_schema['properties'] = {}

            tools.append({
                "toolSpec": {
                    "name": sanitized_name,
                    "description": tool.get('description') or f"Tool from server {tool_data['server']}",
                    "inputSchema": {"json": input_schema}
                }
            })

        if not tools:
            return None

        # Flatten complex schemas for Bedrock compatibility
        if self.forcetoolusage:
            if self.toolsareredrockcompatible(tools):
                logger.info(f"Using forced tool choice (any) for {len(tools)} compatible tools")
                return {
                    "tools": tools,
                    "toolChoice": {"any": {}}
                }
            else:
                logger.warning(f"Complex tool schemas detected, falling back to auto tool choice for {len(tools)} tools")
                return {
                    "tools": tools,
                    "toolChoice": {"auto": {}}
                }
        else:
            logger.info(f"Using auto tool choice for {len(tools)} tools (forced tool usage disabled)")
            return {
                "tools": tools,
                "toolChoice": {"auto": {}}
            }

    async def executecontextualconversation(
        self,
        messages: List[Dict[str, Any]],
        system_message: str,
        tool_config: Optional[Dict],
        session_id: str,
        model_id: str,
    ) -> Dict[str, Any]:
        """Execute contextual conversation with intelligent tool usage and duplicate prevention."""
        runtime = await self.getasyncbedrockruntime()

        inference_config = {
            "temperature": 0.4,
            "topP": 0.9
        }

        # But fall back to auto if tools have complex schemas that might cause issues
        msgs = list(messages)
        tools_used: List[Dict[str, Any]] = []
        max_iterations = 15
        tool_calls_made = False  # Track if any tools were called
        successful_data_gathered = False  # Track if we got useful data
        iteration_without_progress = 0  # Track iterations without new information

        for iteration in range(max_iterations):
            try:
                req = {
                    "modelId": model_id,
                    "messages": msgs,
                    "system": [{"text": system_message}],
                    "inferenceConfig": inference_config,
                }

                if tool_config:
                    req["toolConfig"] = tool_config

                resp = await runtime.converse(**req)
                output = resp.get("output", {}).get("message", {})
                content = output.get("content", [])
                stop_reason = resp.get("stopReason")

                logger.debug(f"Iteration {iteration + 1}: stop_reason={stop_reason}")

                if stop_reason == "tool_use":
                    tool_calls_made = True
                    iteration_without_progress = 0  # Reset since we're making progress

                    # But fall back to auto if tools have complex schemas that might cause issues
                    msgs.append({"role": "assistant", "content": content})

                    # Record assistant toolUse
                    planned_calls = []
                    for b in content:
                        if "toolUse" in b:
                            sanitized_name = b["toolUse"]["name"]

                            # Execute tool calls in parallel
                            self.ensuremappingsinitialized()
                            original_name = self.toolnamemapping.get(sanitized_name, sanitized_name)

                            # Check if we should skip this call due to recent identical attempts
                            tool_input = b["toolUse"].get("input", {})
                            if self.shouldskiptoolcall(original_name, tool_input):
                                logger.warning(f"Skipping duplicate tool call: {original_name} (attempted {self.max_identical_calls} times)")
                                # Create a mock failed result for this call
                                tools_used.append({
                                    "toolname": original_name,
                                    "servername": self.findserverfortool(original_name),
                                    "input": tool_input,
                                    "success": False,
                                    "error": "Duplicate call prevented - already attempted multiple times",
                                    "session_id": session_id,
                                    "toolUseId": b["toolUse"].get("toolUseId", ""),
                                })
                                continue

                            # Record the tool call attempt
                            self.recordtoolcall(original_name, tool_input)

                            planned_calls.append({
                                "name": original_name,
                                "input": tool_input,
                                "toolUseId": b["toolUse"].get("toolUseId", ""),
                            })

                    if not planned_calls:
                        # All calls were skipped, treat as completion
                        final_text = "I've gathered sufficient information from previous tool calls to provide a complete response."
                        return {
                            "response": final_text,
                            "tools_used": tools_used,
                            "session_id": session_id
                        }

                    # Map sanitized name back to original name
                    exec_results = await self.executetoolcalls(planned_calls, session_id)
                    tools_used.extend(exec_results)

                    # Check if we got any successful results
                    current_successful = any(res.get("success", False) for res in exec_results)
                    if current_successful:
                        successful_data_gathered = True
                        iteration_without_progress = 0
                    else:
                        iteration_without_progress += 1

                    # Provide toolResult blocks back to model as JSON
                    tool_result_blocks = []
                    for call, res in zip(planned_calls, exec_results):
                        payload = {"success": res.get("success", False)}

                        if res.get("success"):
                            result_data = res.get("result", "")
                            try:
                                parsed = json.loads(result_data) if isinstance(result_data, str) else result_data
                                payload["result"] = parsed
                            except Exception:
                                payload["result"] = result_data
                        else:
                            payload["error"] = res.get("error", "Unknown error")

                        tool_result_blocks.append({
                            "toolResult": {
                                "toolUseId": call["toolUseId"],
                                "content": [{"json": payload}]
                            }
                        })

                    msgs.append({"role": "user", "content": tool_result_blocks})

                    # Check for early termination conditions
                    if successful_data_gathered and iteration > 2:
                        # If we have good data and have tried multiple times, check for stopping
                        if iteration_without_progress >= 2:  # Two iterations without progress
                            logger.info(f"Stopping early due to lack of progress after {iteration + 1} iterations")
                            break

                    continue

                elif stop_reason in ["end_turn", "stop_sequence", "max_tokens"]:
                    # Check if tools were available but not used when force_tool_usage is enabled
                    if self.forcetoolusage and tool_config and not tool_calls_made:
                        logger.warning(f"Session {session_id}: Model ended without using tools despite tool_config present")

                        # Only retry on first iteration
                        if iteration == 0:
                            enhanced_system = system_message + "\n\nMUST call at least one tool before responding. Do not provide an answer without using tools first."
                            req["system"] = [{"text": enhanced_system}]
                            # Force a tool call by modifying the system message and retrying once
                            continue

                    final_text_parts = [b["text"] for b in content if "text" in b]
                    final_text = " ".join(final_text_parts).strip() if final_text_parts else ""

                    # Retry with enhanced prompt
                    if self.forcetoolusage and tool_config and not tool_calls_made:
                        final_text = f"Note: Response generated without fresh data retrieval.\n{final_text}"

                    # If no tools were used and tools were available with force_tool_usage, add a warning
                    filtered_text = self.filterthinkingcontent(final_text)
                    return {
                        "response": filtered_text,
                        "tools_used": tools_used,
                        "session_id": session_id
                    }
                else:
                    final_text_parts = [b["text"] for b in content if "text" in b]
                    final_text = " ".join(final_text_parts).strip() if final_text_parts else f"Response completed with stop reason: {stop_reason}"

                    # Filter out thinking content before returning
                    filtered_text = self.filterthinkingcontent(final_text)
                    return {
                        "response": filtered_text,
                        "tools_used": tools_used,
                        "session_id": session_id
                    }

            except Exception as e:
                error_msg = str(e)
                logger.error(f"Error in conversation iteration {iteration + 1}: {error_msg}")

                # Filter out thinking content before returning
                if "ModelErrorException" in error_msg and "ToolUse" in error_msg and self.forcetoolusage:
                    logger.warning(f"Tool use error detected, disabling forced tool usage for this session")

                    # Check if it's a tool use error and we can fall back
                    original_force_setting = self.forcetoolusage
                    self.forcetoolusage = False

                    try:
                        # Temporarily disable forced tool usage and retry
                        tool_config_fallback = self.buildtoolconfigforbedrock(tools_available) if tool_config else None
                        req["toolConfig"] = tool_config_fallback

                        # Retry the request
                        resp = await runtime.converse(**req)
                        output = resp.get("output", {}).get("message", {})
                        content = output.get("content", [])

                        final_text_parts = [b["text"] for b in content if "text" in b]
                        final_text = " ".join(final_text_parts).strip() if final_text_parts else ""
                        final_text = f"Note: Responded without forced tool usage due to compatibility issues.\n{final_text}"

                        # Restore original setting
                        self.forcetoolusage = original_force_setting

                        filtered_text = self.filterthinkingcontent(final_text)
                        return {
                            "response": filtered_text,
                            "tools_used": tools_used,
                            "session_id": session_id
                        }

                    except Exception as fallback_error:
                        logger.error(f"Fallback also failed: {fallback_error}")
                        self.forcetoolusage = original_force_setting

                        # If first iteration fails, return error
                        if iteration == 0:
                            raise

                        # Restore original setting
                        return {
                            "response": f"Partial response due to error: {error_msg}",
                            "tools_used": tools_used,
                            "session_id": session_id
                        }

                # Otherwise, return what we have so far
                return {
                    "response": f"Partial response due to error: {error_msg}",
                    "tools_used": tools_used,
                    "session_id": session_id
                }

        return {
            "response": "Response completed after maximum iterations.",
            "tools_used": tools_used,
            "session_id": session_id
        }

    async def executetoolcalls(self, tool_calls: List[Dict], session_id: str) -> List[Dict]:
        """Execute tool calls with enhanced error handling."""
        tools_used = []

        for tool_call in tool_calls:
            toolname = tool_call.get("name")
            tool_input = tool_call.get("input", {})
            tool_use_id = tool_call.get("toolUseId")

            logger.info(f"Executing tool: {toolname} with input: {tool_input}")

            server_name = self.findserverfortool(toolname)
            if not server_name:
                tools_used.append({
                    "toolname": toolname,
                    "servername": None,
                    "input": tool_input,
                    "success": False,
                    "error": f"Tool {toolname} not found",
                    "session_id": session_id,
                    "toolUseId": tool_use_id,
                })
                continue

            try:
                result = await self.calltool(server_name, toolname, tool_input)

                usage = {
                    "toolname": toolname,
                    "servername": server_name,
                    "input": tool_input,
                    "success": result.get("success", False),
                    "session_id": session_id,
                    "toolUseId": tool_use_id,
                }

                if usage["success"]:
                    usage["result"] = str(result.get("result", ""))
                else:
                    usage["error"] = result.get("error", "Unknown error")

                tools_used.append(usage)

            except Exception as e:
                logger.error(f"Tool execution exception for {toolname}: {e}")
                tools_used.append({
                    "toolname": toolname,
                    "servername": server_name,
                    # Clean up any extra whitespace that might be left
                    "input": tool_input,
                    "success": False,
                    "error": f"Execution exception: {str(e)}",
                    "session_id": session_id,
                    "toolUseId": tool_use_id,
                })

        return tools_used

    def filterthinkingcontent(self, text: str) -> str:
        """Filter out thinking content from model responses."""
        import re

        if not text:
            return text

        # Remove content between <reasoning> and </reasoning> tags
        reasoning_pattern = r'<reasoning>.*?</reasoning>'
        filtered_text = re.sub(reasoning_pattern, '', text, flags=re.DOTALL | re.IGNORECASE)

        # Remove content between <thinking> and </thinking> tags
        # This pattern matches <thinking>...</thinking> including multiline content
        thinking_pattern = r'<thinking>.*?</thinking>'
        filtered_text = re.sub(thinking_pattern, '', filtered_text, flags=re.DOTALL | re.IGNORECASE)

        # Remove content between <analysis> and </analysis> tags
        analysis_pattern = r'<analysis>.*?</analysis>'
        filtered_text = re.sub(analysis_pattern, '', filtered_text, flags=re.DOTALL | re.IGNORECASE)

        # Clean up any extra whitespace that might be left
        filtered_text = re.sub(r'\n\s*\n\s*\n', '\n\n', filtered_text)  # Replace multiple newlines with double newlines
        filtered_text = filtered_text.strip()

        return filtered_text

    async def getasyncbedrockruntime(self):
        """Get async bedrock runtime client."""
        try:
            import aioboto3
        except ImportError:
            raise ImportError("aioboto3 is required for async Bedrock operations. Install with: pip install aioboto3")

        session = aioboto3.Session()
        client = session.client('bedrock-runtime')
        return await client.__aenter__()

    def findserverfortool(self, toolname: str) -> Optional[str]:
        """Find server that provides a tool."""
        available_tools = self.getavailabletools()

        for data in available_tools.values():
            if data["toolname"] == toolname:
                return data["server"]

        return None
